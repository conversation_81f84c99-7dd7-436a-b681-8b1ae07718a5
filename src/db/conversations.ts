import { db } from './index';
import { conversations, messages, type Conversation, type NewConversation, type Message, type NewMessage } from './schema';
import { eq, desc, and } from 'drizzle-orm';

/**
 * Conversation database operations
 * 对话数据库操作函数
 */

/**
 * Create a new conversation for a user
 * 为用户创建新对话
 */
export async function createConversation(userId: number, conversationData: {
	id: string;
	title: string;
}): Promise<Conversation> {
	const newConversation: NewConversation = {
		id: conversationData.id,
		userId,
		title: conversationData.title,
	};

	const result = await db.insert(conversations).values(newConversation).returning();
	return result[0];
}

/**
 * Get all conversations for a user
 * 获取用户的所有对话
 */
export async function getUserConversations(userId: number): Promise<Conversation[]> {
	return await db
		.select()
		.from(conversations)
		.where(eq(conversations.userId, userId))
		.orderBy(desc(conversations.updatedAt));
}

/**
 * Get a specific conversation by ID and user
 * 根据ID和用户获取特定对话
 */
export async function getConversationById(conversationId: string, userId: number): Promise<Conversation | null> {
	const result = await db
		.select()
		.from(conversations)
		.where(and(eq(conversations.id, conversationId), eq(conversations.userId, userId)))
		.limit(1);
	
	return result[0] || null;
}

/**
 * Update conversation title
 * 更新对话标题
 */
export async function updateConversationTitle(conversationId: string, userId: number, title: string): Promise<boolean> {
	const result = await db
		.update(conversations)
		.set({ 
			title, 
			updatedAt: new Date().toISOString() 
		})
		.where(and(eq(conversations.id, conversationId), eq(conversations.userId, userId)))
		.returning();
	
	return result.length > 0;
}

/**
 * Delete a conversation and all its messages
 * 删除对话及其所有消息
 */
export async function deleteConversation(conversationId: string, userId: number): Promise<boolean> {
	// First delete all messages in the conversation
	await db.delete(messages).where(eq(messages.conversationId, conversationId));
	
	// Then delete the conversation
	const result = await db
		.delete(conversations)
		.where(and(eq(conversations.id, conversationId), eq(conversations.userId, userId)))
		.returning();
	
	return result.length > 0;
}

/**
 * Add a message to a conversation
 * 向对话添加消息
 */
export async function addMessage(messageData: {
	id: string;
	conversationId: string;
	content: string;
	role: 'user' | 'assistant';
	docReferences?: any[];
}): Promise<Message> {
	const newMessage: NewMessage = {
		id: messageData.id,
		conversationId: messageData.conversationId,
		content: messageData.content,
		role: messageData.role,
		docReferences: messageData.docReferences ? JSON.stringify(messageData.docReferences) : null,
	};

	// Update conversation's updatedAt timestamp
	await db
		.update(conversations)
		.set({ updatedAt: new Date().toISOString() })
		.where(eq(conversations.id, messageData.conversationId));

	const result = await db.insert(messages).values(newMessage).returning();
	return result[0];
}

/**
 * Get all messages for a conversation
 * 获取对话的所有消息
 */
export async function getConversationMessages(conversationId: string): Promise<Message[]> {
	const rawMessages = await db
		.select()
		.from(messages)
		.where(eq(messages.conversationId, conversationId))
		.orderBy(messages.timestamp);

	// Parse docReferences from JSON string
	return rawMessages.map(message => ({
		...message,
		docReferences: message.docReferences ? JSON.parse(message.docReferences) : undefined
	}));
}

/**
 * Get conversation with messages
 * 获取包含消息的完整对话
 */
export async function getConversationWithMessages(conversationId: string, userId: number): Promise<{
	conversation: Conversation;
	messages: Message[];
} | null> {
	const conversation = await getConversationById(conversationId, userId);
	if (!conversation) {
		return null;
	}

	const conversationMessages = await getConversationMessages(conversationId);
	
	return {
		conversation,
		messages: conversationMessages,
	};
}

/**
 * Delete all conversations for a user
 * 删除用户的所有对话
 */
export async function deleteAllUserConversations(userId: number): Promise<void> {
	// Get all conversation IDs for the user
	const userConversations = await db
		.select({ id: conversations.id })
		.from(conversations)
		.where(eq(conversations.userId, userId));

	// Delete all messages for these conversations
	for (const conv of userConversations) {
		await db.delete(messages).where(eq(messages.conversationId, conv.id));
	}

	// Delete all conversations for the user
	await db.delete(conversations).where(eq(conversations.userId, userId));
}
